import '../styles/product-card.scss';

interface ProductCardProps {
    title: string;
    description: string;
    buttonText: string;
    imageUrl?: string;
}

const ProductCard = ({ title, description, buttonText, imageUrl }: ProductCardProps) => {
    return (
        <div className="product-card">
            <div className="product-image" style={{ backgroundImage: imageUrl ? `url(${imageUrl})` : undefined }}>
                {/* Placeholder background if no image provided */}
            </div>
            <div className="text-bubble">
                <h3>{title}</h3>
                <p>{description}</p>
                <button className="product-button">{buttonText}</button>
            </div>
        </div>
    );
};

export default ProductCard;
