import '../styles/product-card.scss';

interface ProductCardProps {
    title: string;
    description: string;
    buttonText: string;
}

const ProductCard = ({ title, description, buttonText }: ProductCardProps) => {
    return (
        <div className="product-card">
            <div className="product-sky">
                <div className="cloud cloud-1"></div>
                <div className="cloud cloud-2"></div>
                <div className="cloud cloud-3"></div>
            </div>
            <div className="product-info">
                <h3>{title}</h3>
                <p>{description}</p>
                <button className="product-button">{buttonText}</button>
            </div>
        </div>
    );
};

export default ProductCard;
