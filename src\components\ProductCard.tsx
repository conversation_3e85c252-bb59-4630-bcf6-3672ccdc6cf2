import '../styles/product-card.scss';

interface ProductCardProps {
    title: string;
    description: string;
    imageUrl: string;
    buttonText: string;
}

const ProductCard = ({ title, description, imageUrl, buttonText }: ProductCardProps) => {
    return (
        <div className="product-card">
            <div className="product-image">
                <img src={imageUrl} alt={title} />
            </div>
            <div className="product-info">
                <h3>{title}</h3>
                <p>{description}</p>
                <button className="product-button">{buttonText}</button>
            </div>
        </div>
    );
};

export default ProductCard;
