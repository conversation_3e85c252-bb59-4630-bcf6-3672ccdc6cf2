.product-card {
    background-color: #8FBC8F; // Green background for entire card
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 320px;
    height: 380px;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;

    &:hover {
        transform: translateY(-5px);
    }

    .product-sky {
        width: 100%;
        height: 200px;
        background: linear-gradient(180deg, #87CEEB 0%, #B0E0E6 100%);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .cloud {
            position: absolute;
            background-color: white;
            border-radius: 50px;
            opacity: 1;

            &::before,
            &::after {
                content: '';
                position: absolute;
                background-color: white;
                border-radius: 50px;
            }
        }

        .cloud-1 {
            width: 50px;
            height: 25px;
            top: 25%;
            left: 15%;

            &::before {
                width: 35px;
                height: 35px;
                top: -18px;
                left: 8px;
            }

            &::after {
                width: 25px;
                height: 25px;
                top: -12px;
                right: 8px;
            }
        }

        .cloud-2 {
            width: 70px;
            height: 30px;
            top: 15%;
            right: 20%;

            &::before {
                width: 45px;
                height: 45px;
                top: -22px;
                left: 12px;
            }

            &::after {
                width: 30px;
                height: 30px;
                top: -15px;
                right: 12px;
            }
        }

        .cloud-3 {
            width: 40px;
            height: 20px;
            bottom: 30%;
            left: 40%;

            &::before {
                width: 25px;
                height: 25px;
                top: -12px;
                left: 6px;
            }

            &::after {
                width: 20px;
                height: 20px;
                top: -10px;
                right: 6px;
            }
        }
    }

    .product-info {
        padding: 25px 20px 20px;
        text-align: center;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .text-bubble {
            background-color: white;
            border-radius: 15px;
            padding: 15px 18px;
            margin-bottom: 20px;
            width: calc(100% - 20px);
            box-sizing: border-box;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            h3 {
                color: #333;
                font-size: 16px;
                margin: 0 0 10px 0;
                font-weight: 700;
                line-height: 1.2;
                text-align: center;
            }

            p {
                color: #333;
                font-size: 12px;
                line-height: 1.4;
                margin: 0;
                text-align: left;
            }
        }

        .product-button {
            background-color: white;
            color: #333;
            border: none;
            padding: 12px 35px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            &:hover {
                background-color: #f8f8f8;
                transform: scale(1.05);
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
            }
        }
    }
}
