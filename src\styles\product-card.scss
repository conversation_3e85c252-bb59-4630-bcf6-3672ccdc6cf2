.product-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 280px;
    transition: transform 0.3s ease;
    
    &:hover {
        transform: translateY(-5px);
    }
    
    .product-image {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        // Placeholder for when no image is provided
        &::after {
            content: '🌿';
            font-size: 48px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
    
    .product-info {
        padding: 20px;
        text-align: center;
        
        h3 {
            color: #333;
            font-size: 18px;
            margin: 0 0 10px 0;
            font-weight: 600;
        }
        
        p {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
            margin: 0 0 15px 0;
        }
        
        .product-button {
            background-color: #90EE90;
            color: #333;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            
            &:hover {
                background-color: #7FDD7F;
            }
        }
    }
}
