.product-card {
    position: relative;
    width: 320px;
    height: 320px; // Square dimensions
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .product-image {
        width: 100%;
        height: 100%;
        background-color: #8FBC8F; // Fallback green background
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        // Default gradient background when no image is provided
        background-image: linear-gradient(135deg, #8FBC8F 0%, #7AB87A 50%, #6BA46B 100%);
    }

    .text-bubble {
        position: absolute;
        bottom: 20px;
        left: 20px;
        right: 20px;
        background-color: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);

        h3 {
            color: #333;
            font-size: 18px;
            margin: 0 0 10px 0;
            font-weight: 700;
            line-height: 1.2;
            text-align: left; // Left-aligned title
        }

        p {
            color: #555;
            font-size: 13px;
            line-height: 1.4;
            margin: 0 0 15px 0;
            text-align: left;
        }

        .product-button {
            background-color: #8FBC8F;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            white-space: nowrap;

            &:hover {
                background-color: #7AB87A;
                transform: scale(1.05);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            }
        }
    }
}
