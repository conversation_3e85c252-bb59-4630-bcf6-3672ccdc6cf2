.product-card {
    background-color: #8FBC8F; // Green background for entire card
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 300px;
    height: 350px;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;

    &:hover {
        transform: translateY(-5px);
    }

    .product-sky {
        width: 100%;
        height: 180px;
        background: linear-gradient(180deg, #87CEEB 0%, #B0E0E6 100%);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .cloud {
            position: absolute;
            background-color: white;
            border-radius: 50px;
            opacity: 0.9;

            &::before,
            &::after {
                content: '';
                position: absolute;
                background-color: white;
                border-radius: 50px;
            }
        }

        .cloud-1 {
            width: 60px;
            height: 30px;
            top: 30%;
            left: 20%;

            &::before {
                width: 40px;
                height: 40px;
                top: -20px;
                left: 10px;
            }

            &::after {
                width: 30px;
                height: 30px;
                top: -15px;
                right: 10px;
            }
        }

        .cloud-2 {
            width: 80px;
            height: 35px;
            top: 20%;
            right: 15%;

            &::before {
                width: 50px;
                height: 50px;
                top: -25px;
                left: 15px;
            }

            &::after {
                width: 35px;
                height: 35px;
                top: -20px;
                right: 15px;
            }
        }

        .cloud-3 {
            width: 45px;
            height: 25px;
            bottom: 25%;
            left: 35%;

            &::before {
                width: 30px;
                height: 30px;
                top: -15px;
                left: 8px;
            }

            &::after {
                width: 25px;
                height: 25px;
                top: -12px;
                right: 8px;
            }
        }
    }

    .product-info {
        padding: 20px;
        text-align: center;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .text-bubble {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 15px;
            width: 100%;
            box-sizing: border-box;

            h3 {
                color: #333;
                font-size: 16px;
                margin: 0 0 8px 0;
                font-weight: 600;
                line-height: 1.2;
            }

            p {
                color: #333;
                font-size: 11px;
                line-height: 1.3;
                margin: 0;
                text-align: left;
            }
        }

        .product-button {
            background-color: white;
            color: #333;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            white-space: nowrap;

            &:hover {
                background-color: #f0f0f0;
                transform: scale(1.05);
            }
        }
    }
}
