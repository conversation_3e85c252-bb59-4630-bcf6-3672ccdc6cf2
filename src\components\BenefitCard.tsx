import '../styles/benefit-card.scss';

interface BenefitCardProps {
    title: string;
    description: string;
    backgroundColor: string;
}

const BenefitCard = ({ title, description, backgroundColor }: BenefitCardProps) => {
    return (
        <div className="benefit-card" style={{ backgroundColor }}>
            <h4>{title}</h4>
            <p>{description}</p>
        </div>
    );
};

export default BenefitCard;
