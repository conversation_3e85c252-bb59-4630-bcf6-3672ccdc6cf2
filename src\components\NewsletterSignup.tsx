import { useState } from 'react';

const NewsletterSignup = () => {
    const [email, setEmail] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle newsletter signup logic here
        console.log('Newsletter signup:', email);
        setEmail('');
    };

    return (
        <section className="newsletter">
            <div className="newsletter-content">
                <h3>Get 10% Off Your First Order</h3>
                <p>Sign up for exclusive offers and updates!</p>
                <form className="newsletter-form" onSubmit={handleSubmit}>
                    <input
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                    />
                    <button type="submit">Subscribe</button>
                </form>
            </div>
        </section>
    );
};

export default NewsletterSignup;
