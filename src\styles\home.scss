// Hero Section
.logo {
    bottom: 40px;
    left: 260px;
}

.horizontal-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    margin: 40px 0;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    background-color: #db98c4;
    padding: 20px;
    width: 450px;
    height: 120px;
    text-align: center;
}

.ad-container {
    position: relative;
    top: 50px;
    left: 700px;
}

button {
    border-radius: 40px;
    color: black;
    border: none;
    width: 100px;
    height: 25px;
    background-color: #ffffff;
    cursor: pointer;
    font-weight: 500;

    &:hover {
        background-color: #f0f0f0;
    }
}

.home-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    padding: 0 20px;

    h2 {
        color: #b28aa5;
        text-align: center;
        font-size: 24px;
        max-width: 800px;
    }
}

// Featured Products Section
.featured-products {
    text-align: center;
    margin: 60px 0;

    h2 {
        color: #333;
        font-size: 32px;
        margin-bottom: 40px;
    }

    .products-grid {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        padding: 0 20px;
    }
}

// Benefits Section
.benefits-section {
    text-align: center;
    margin: 60px 0;

    h2 {
        color: #333;
        font-size: 32px;
        margin-bottom: 40px;
    }

    .benefits-grid {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        padding: 0 20px;
    }
}

// About Us Section
.about-us {
    text-align: center;
    margin: 60px 0;
    padding: 0 20px;

    h2 {
        color: #333;
        font-size: 32px;
        margin-bottom: 30px;
    }

    .about-content {
        max-width: 800px;
        margin: 0 auto;
        background-color: #db98c4;
        padding: 30px;
        border-radius: 20px;

        p {
            color: #333;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
        }
    }
}

// Newsletter Section
.newsletter {
    text-align: center;
    margin: 60px 0;
    padding: 0 20px;

    .newsletter-content {
        background-color: #db98c4;
        padding: 30px;
        border-radius: 20px;
        max-width: 600px;
        margin: 0 auto;

        h3 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }

        p {
            color: #333;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .newsletter-form {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;

            input {
                padding: 10px 15px;
                border: none;
                border-radius: 25px;
                font-size: 14px;
                min-width: 200px;

                &:focus {
                    outline: none;
                    box-shadow: 0 0 5px rgba(0,0,0,0.3);
                }
            }

            button {
                padding: 10px 20px;
                background-color: #333;
                color: white;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-size: 14px;

                &:hover {
                    background-color: #555;
                }
            }
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .horizontal-container {
        flex-direction: column;
        align-items: center;
        padding: 0 20px;

        .logo {
            position: static;
            margin-bottom: 20px;
        }

        .ad-container {
            position: static;
            width: 100%;
            max-width: 400px;
        }
    }

    .products-grid,
    .benefits-grid {
        flex-direction: column;
        align-items: center;
    }

    .home-title h2 {
        font-size: 20px;
        padding: 0 20px;
    }

    .featured-products h2,
    .benefits-section h2,
    .about-us h2 {
        font-size: 28px;
    }
}

// Global button styling override for home page
.home-page {
    button {
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.95);
        }
    }
}