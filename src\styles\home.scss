// Hero Section
.logo {
    bottom: 40px;
    left: 260px;
}

.horizontal-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    margin: 40px 0;
    position: relative;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    background-color: #db98c4;
    padding: 25px 30px;
    width: 480px;
    height: auto;
    min-height: 140px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    p {
        color: #333;
        font-size: 16px;
        font-weight: 600;
        line-height: 1.4;
        margin: 0 0 20px 0;
        text-align: center;
    }
}

.ad-container {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
}

.container button {
    border-radius: 25px;
    color: #333;
    border: none;
    padding: 10px 25px;
    min-width: 120px;
    height: auto;
    background-color: #ffffff;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
        background-color: #f8f8f8;
        transform: scale(1.05);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    }
}

.home-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    padding: 0 20px;

    h2 {
        color: #b28aa5;
        text-align: center;
        font-size: 24px;
        max-width: 800px;
    }
}

// Featured Products Section
.featured-products {
    text-align: center;
    margin: 60px 0;

    h2 {
        color: #333;
        font-size: 32px;
        margin-bottom: 40px;
    }

    .products-grid {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        padding: 0 20px;
    }
}

// Benefits Section
.benefits-section {
    text-align: center;
    margin: 60px 0;

    h2 {
        color: #333;
        font-size: 32px;
        margin-bottom: 40px;
    }

    .benefits-grid {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        padding: 0 20px;
    }
}

// About Us Section
.about-us {
    text-align: center;
    margin: 60px 0;
    padding: 0 20px;

    h2 {
        color: #333;
        font-size: 32px;
        margin-bottom: 30px;
    }

    .about-content {
        max-width: 800px;
        margin: 0 auto;
        background-color: #db98c4;
        padding: 30px;
        border-radius: 20px;

        p {
            color: #333;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
        }
    }
}

// Newsletter Section
.newsletter {
    text-align: center;
    margin: 60px 0;
    padding: 0 20px;

    .newsletter-content {
        background-color: #db98c4;
        padding: 30px;
        border-radius: 20px;
        max-width: 600px;
        margin: 0 auto;

        h3 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }

        p {
            color: #333;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .newsletter-form {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;

            input {
                padding: 10px 15px;
                border: none;
                border-radius: 25px;
                font-size: 14px;
                min-width: 200px;

                &:focus {
                    outline: none;
                    box-shadow: 0 0 5px rgba(0,0,0,0.3);
                }
            }

            button {
                padding: 10px 20px;
                background-color: #333;
                color: white;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-size: 14px;

                &:hover {
                    background-color: #555;
                }
            }
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .horizontal-container {
        flex-direction: column;
        align-items: center;
        padding: 0 20px;
        margin: 20px 0;

        .logo {
            position: static;
            margin-bottom: 30px;
        }

        .ad-container {
            position: static;
            transform: none;
            width: 100%;
            max-width: 400px;
        }

        .container {
            width: 100%;
            max-width: 400px;
            padding: 20px 25px;

            p {
                font-size: 14px;
                margin-bottom: 15px;
            }
        }
    }

    .products-grid,
    .benefits-grid {
        flex-direction: column;
        align-items: center;
    }

    .home-title h2 {
        font-size: 20px;
        padding: 0 20px;
    }

    .featured-products h2,
    .benefits-section h2,
    .about-us h2 {
        font-size: 28px;
    }
}

// Global button styling override for home page
.home-page {
    button {
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.95);
        }
    }
}