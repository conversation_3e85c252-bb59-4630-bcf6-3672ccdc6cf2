import '../styles/home.scss';
import Divider from "../components/Divider";
import Header from "../components/Header";
import Logo from "../components/Logo";
import ProductCard from "../components/ProductCard";
import BenefitCard from "../components/BenefitCard";
import NewsletterSignup from "../components/NewsletterSignup";
import Footer from "../components/Footer";

const Home = () => {
    return (
        <div className="home-page">
            <Divider />
            <Header />

            {/* Hero Section */}
            <section className='horizontal-container'>
                <Logo />
                <div className="container ad-container">
                    <p>
                        Pure, Relaxing, and Non-Toxic Self-Care for Every Body
                        Transform your bath time into a luxurious self-care ritual with our clean, handcrafted products.
                    </p>
                    <button>
                        Shop Now
                    </button>
                </div>
            </section>

            <div className='home-title'>
                <h2>
                    Combining natures superpowers for natural health and well-being.
                </h2>
            </div>

            <Divider />

            {/* Featured Products Section */}
            <section className="featured-products">
                <h2>Featured Products</h2>
                <div className="products-grid">
                    <ProductCard
                        title="Bedtime bliss!"
                        description="This bathbomb is just what you need to relax before bed and set yourself up for a peaceful night's sleep!"
                        buttonText="quick buy"
                    />
                    <ProductCard
                        title="Uplift and destress!"
                        description="Just one pinch! Use this salt to ease the evening stress in need of a pick-me-up and to destress! Then rinse the bathbomb for more!"
                        buttonText="quick buy"
                    />
                    <ProductCard
                        title="Bedtime bliss!"
                        description="This bathbomb is just what you need to relax before bed and set yourself up for a peaceful night's sleep!"
                        buttonText="quick buy"
                    />
                </div>
            </section>

            <Divider />

            {/* Benefits Section */}
            <section className="benefits-section">
                <h2>Why You'll Love Our Bath Salts</h2>
                <div className="benefits-grid">
                    <BenefitCard
                        title="Exfoliating"
                        description="Gently removes dead skin cells for smoother, softer skin"
                        backgroundColor="#E6B3FF"
                    />
                    <BenefitCard
                        title="Detoxifying"
                        description="Helps remove toxins and impurities from your skin"
                        backgroundColor="#B3D9FF"
                    />
                    <BenefitCard
                        title="Hydrating"
                        description="Locks in moisture for long-lasting skin hydration"
                        backgroundColor="#FFB3E6"
                    />
                </div>
            </section>

            <Divider />

            {/* About Us Section */}
            <section className="about-us">
                <h2>About Us</h2>
                <div className="about-content">
                    <p>
                        At Scentsationally Pure, our founder Lucy created the company out of a personal need for safe,
                        non-toxic products when dealing with health challenges. Today, we craft beautiful
                        self-care products designed for everyone.
                    </p>
                </div>
            </section>

            <Divider />

            {/* Newsletter Signup */}
            <NewsletterSignup />

            <Divider />

            {/* Footer */}
            <Footer />
        </div>
    );
};

export default Home;
